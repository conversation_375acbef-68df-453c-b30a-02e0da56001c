# Database Schema

PostgreSQL database schema cho hệ thống E-Commerce.

## Cấu trúc Database

### Core Tables

1. **users** - Thông tin người dùng
2. **user_addresses** - Địa chỉ của người dùng
3. **categories** - <PERSON><PERSON> mục sản phẩm (hỗ trợ nested categories)
4. **products** - Sản phẩm chính
5. **product_variants** - Biến thể sản phẩm (size, color, etc.)
6. **product_images** - Hình ảnh sản phẩm
7. **product_categories** - Liên kết sản phẩm với danh mục

### Shopping & Orders

8. **cart** - Giỏ hàng (hỗ trợ cả user và guest)
9. **cart_items** - Items trong giỏ hàng
10. **orders** - Đơn hàng
11. **order_items** - Items trong đơn hàng
12. **payments** - Thông tin thanh toán

### Additional Features

13. **coupons** - Mã giảm giá
14. **reviews** - <PERSON><PERSON><PERSON> gi<PERSON> sản phẩm
15. **wishlist** - <PERSON><PERSON> s<PERSON><PERSON> yêu thích

## Features

- **UUID Primary Keys** - Sử dụng UUID thay vì auto-increment integers
- **Soft Deletes** - Sử dụng is_active flags
- **Timestamps** - Tự động cập nhật created_at và updated_at
- **Indexing** - Indexes cho performance tối ưu
- **Constraints** - Foreign keys và check constraints
- **JSONB Support** - Lưu trữ flexible data như attributes, dimensions

## Setup

1. Chạy PostgreSQL server
2. Tạo database: `CREATE DATABASE ecommerce_db;`
3. Chạy init script: `psql -d ecommerce_db -f init.sql`
4. (Optional) Load seed data: `psql -d ecommerce_db -f seed.sql`

## Environment Variables

```
DATABASE_URL=postgresql://username:password@localhost:5432/ecommerce_db
```

## Sample Queries

### Get products with categories
```sql
SELECT p.*, c.name as category_name 
FROM products p
JOIN product_categories pc ON p.id = pc.product_id
JOIN categories c ON pc.category_id = c.id
WHERE p.is_active = true;
```

### Get user's cart with items
```sql
SELECT ci.*, p.name, p.price, pv.attributes
FROM cart_items ci
JOIN cart c ON ci.cart_id = c.id
JOIN products p ON ci.product_id = p.id
LEFT JOIN product_variants pv ON ci.variant_id = pv.id
WHERE c.user_id = $1;
```

### Get order with items
```sql
SELECT o.*, oi.product_name, oi.quantity, oi.unit_price
FROM orders o
JOIN order_items oi ON o.id = oi.order_id
WHERE o.id = $1;
```
