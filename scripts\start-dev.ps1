# Development startup script
Write-Host "Starting E-Commerce Platform in Development Mode..." -ForegroundColor Green

# Function to start a service in a new terminal
function Start-Service($name, $path, $command) {
    Write-Host "Starting $name..." -ForegroundColor Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$path'; $command"
}

# Start PostgreSQL and Redis
Write-Host "Starting database services..." -ForegroundColor Yellow
docker-compose up postgres redis -d

# Wait for database to be ready
Write-Host "Waiting for database to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Start Backend API
Start-Service "Backend API (FastAPI)" "$PWD\backend-api" "& .\venv\Scripts\Activate.ps1; python main.py"

# Start Backend Services
Start-Service "Backend Services (Node.js)" "$PWD\backend-services" "npm run dev"

# Start Frontend
Start-Service "Frontend (Next.js)" "$PWD\frontend" "npm run dev"

Write-Host "All services are starting..." -ForegroundColor Green
Write-Host "Services will be available at:" -ForegroundColor Yellow
Write-Host "- Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "- Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "- Backend Services: http://localhost:3001" -ForegroundColor White
Write-Host "- API Documentation: http://localhost:8000/docs" -ForegroundColor White

Write-Host "Press any key to stop all services..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop all services
Write-Host "Stopping services..." -ForegroundColor Red
docker-compose down
Get-Process | Where-Object {$_.ProcessName -eq "python" -or $_.ProcessName -eq "node"} | Stop-Process -Force
