from sqlalchemy import Column, <PERSON>, <PERSON>olean, DateTime, Integer, Numeric, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from ..core.database import Base


class Coupon(Base):
    __tablename__ = "coupons"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(50), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    discount_type = Column(String(20), nullable=False)  # percentage, fixed_amount
    discount_value = Column(Numeric(10, 2), nullable=False)
    minimum_amount = Column(Numeric(10, 2))
    maximum_discount = Column(Numeric(10, 2))
    usage_limit = Column(Integer)
    used_count = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    starts_at = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    @property
    def is_valid(self):
        """Check if coupon is currently valid."""
        from datetime import datetime
        now = datetime.utcnow()
        
        if not self.is_active:
            return False
        
        if self.starts_at and now < self.starts_at:
            return False
        
        if self.expires_at and now > self.expires_at:
            return False
        
        if self.usage_limit and self.used_count >= self.usage_limit:
            return False
        
        return True

    def calculate_discount(self, amount: float) -> float:
        """Calculate discount amount for given order amount."""
        if not self.is_valid:
            return 0
        
        if self.minimum_amount and amount < self.minimum_amount:
            return 0
        
        if self.discount_type == "percentage":
            discount = amount * (self.discount_value / 100)
            if self.maximum_discount:
                discount = min(discount, self.maximum_discount)
            return discount
        elif self.discount_type == "fixed_amount":
            return min(self.discount_value, amount)
        
        return 0
