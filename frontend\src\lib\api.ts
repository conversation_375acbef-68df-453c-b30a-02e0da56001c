import axios from 'axios'
import Cookies from 'js-cookie'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
const SERVICES_BASE_URL = process.env.NEXT_PUBLIC_SERVICES_URL || 'http://localhost:3001'

// Main API client for FastAPI backend
export const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Services API client for Node.js services
export const servicesClient = axios.create({
  baseURL: `${SERVICES_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = Cookies.get('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      Cookies.remove('access_token')
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
    }
    return Promise.reject(error)
  }
)

// API functions
export const api = {
  // Auth
  auth: {
    login: (credentials: { username: string; password: string }) =>
      apiClient.post('/auth/login', credentials, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      }),
    register: (userData: any) => apiClient.post('/auth/register', userData),
    me: () => apiClient.get('/auth/me'),
  },

  // Products
  products: {
    getAll: (params?: any) => apiClient.get('/products', { params }),
    getById: (id: string) => apiClient.get(`/products/${id}`),
    getFeatured: () => apiClient.get('/products?is_featured=true'),
  },

  // Categories
  categories: {
    getAll: () => apiClient.get('/categories'),
    getById: (id: string) => apiClient.get(`/categories/${id}`),
  },

  // Cart
  cart: {
    get: () => apiClient.get('/cart'),
    addItem: (item: any) => apiClient.post('/cart/items', item),
    updateItem: (itemId: string, data: any) => apiClient.put(`/cart/items/${itemId}`, data),
    removeItem: (itemId: string) => apiClient.delete(`/cart/items/${itemId}`),
    clear: () => apiClient.delete('/cart'),
  },

  // Orders
  orders: {
    getAll: () => apiClient.get('/orders'),
    getById: (id: string) => apiClient.get(`/orders/${id}`),
    create: (orderData: any) => apiClient.post('/orders', orderData),
  },

  // Users
  users: {
    getProfile: () => apiClient.get('/users/me'),
    updateProfile: (data: any) => apiClient.put('/users/me', data),
  },
}

// Services API functions
export const services = {
  // Email
  email: {
    sendWelcome: (data: { email: string; name: string }) =>
      servicesClient.post('/email/welcome', data),
    sendOrderConfirmation: (data: { email: string; orderData: any }) =>
      servicesClient.post('/email/order-confirmation', data),
  },

  // Upload
  upload: {
    single: (file: File, options?: any) => {
      const formData = new FormData()
      formData.append('file', file)
      if (options) {
        Object.keys(options).forEach(key => {
          formData.append(key, options[key])
        })
      }
      return servicesClient.post('/upload/single', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
    },
    multiple: (files: File[], options?: any) => {
      const formData = new FormData()
      files.forEach(file => formData.append('files', file))
      if (options) {
        Object.keys(options).forEach(key => {
          formData.append(key, options[key])
        })
      }
      return servicesClient.post('/upload/multiple', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
    },
  },

  // Notifications
  notifications: {
    send: (data: { room: string; event: string; data: any }) =>
      servicesClient.post('/notifications/send', data),
    broadcast: (data: { event: string; data: any }) =>
      servicesClient.post('/notifications/broadcast', data),
  },
}
