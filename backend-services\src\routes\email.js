const express = require('express');
const Joi = require('joi');
const emailService = require('../services/emailService');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const sendEmailSchema = Joi.object({
  to: Joi.string().email().required(),
  subject: Joi.string().required(),
  html: Joi.string().required(),
  text: Joi.string().optional()
});

const welcomeEmailSchema = Joi.object({
  email: Joi.string().email().required(),
  name: Joi.string().required()
});

const orderConfirmationSchema = Joi.object({
  email: Joi.string().email().required(),
  orderData: Joi.object({
    orderNumber: Joi.string().required(),
    totalAmount: Joi.number().required()
  }).required()
});

// Send generic email
router.post('/send', async (req, res) => {
  try {
    const { error, value } = sendEmailSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const result = await emailService.sendEmail(value);
    res.json({ success: true, messageId: result.messageId });
  } catch (err) {
    logger.error('Email send failed', { error: err.message });
    res.status(500).json({ error: 'Failed to send email' });
  }
});

// Send welcome email
router.post('/welcome', async (req, res) => {
  try {
    const { error, value } = welcomeEmailSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const result = await emailService.sendWelcomeEmail(value.email, value.name);
    res.json({ success: true, messageId: result.messageId });
  } catch (err) {
    logger.error('Welcome email send failed', { error: err.message });
    res.status(500).json({ error: 'Failed to send welcome email' });
  }
});

// Send order confirmation email
router.post('/order-confirmation', async (req, res) => {
  try {
    const { error, value } = orderConfirmationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const result = await emailService.sendOrderConfirmation(value.email, value.orderData);
    res.json({ success: true, messageId: result.messageId });
  } catch (err) {
    logger.error('Order confirmation email send failed', { error: err.message });
    res.status(500).json({ error: 'Failed to send order confirmation email' });
  }
});

module.exports = router;
