version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:14
    container_name: ecommerce_postgres
    environment:
      POSTGRES_DB: ecommerce_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ecommerce_network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: ecommerce_redis
    ports:
      - "6379:6379"
    networks:
      - ecommerce_network

  # FastAPI Backend
  backend-api:
    build: ./backend-api
    container_name: ecommerce_backend_api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***********************************************/ecommerce_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend-api:/app
    networks:
      - ecommerce_network

  # Node.js Services
  backend-services:
    build: ./backend-services
    container_name: ecommerce_backend_services
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=***********************************************/ecommerce_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend-services:/app
    networks:
      - ecommerce_network

  # Next.js Frontend
  frontend:
    build: ./frontend
    container_name: ecommerce_frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_SERVICES_URL=http://localhost:3001
    depends_on:
      - backend-api
      - backend-services
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - ecommerce_network

volumes:
  postgres_data:

networks:
  ecommerce_network:
    driver: bridge
