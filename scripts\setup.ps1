# E-Commerce Setup Script for Windows
Write-Host "Setting up E-Commerce Platform..." -ForegroundColor Green

# Check if required tools are installed
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

Write-Host "Checking prerequisites..." -ForegroundColor Yellow

# Check Node.js
if (-not (Test-Command "node")) {
    Write-Host "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Check Python
if (-not (Test-Command "python")) {
    Write-Host "Python is not installed. Please install Python 3.9+ from https://python.org/" -ForegroundColor Red
    exit 1
}

# Check Docker
if (-not (Test-Command "docker")) {
    Write-Host "Docker is not installed. Please install Docker Desktop from https://docker.com/" -ForegroundColor Red
    exit 1
}

Write-Host "All prerequisites are installed!" -ForegroundColor Green

# Create .env files
Write-Host "Creating environment files..." -ForegroundColor Yellow

# Copy .env.example to .env
if (Test-Path ".env.example") {
    Copy-Item ".env.example" ".env"
    Write-Host ".env file created from .env.example" -ForegroundColor Green
} else {
    Write-Host ".env.example not found!" -ForegroundColor Red
}

# Setup Backend API
Write-Host "Setting up Backend API (FastAPI)..." -ForegroundColor Yellow
Set-Location "backend-api"

# Create virtual environment
python -m venv venv
& ".\venv\Scripts\Activate.ps1"

# Install dependencies
pip install -r requirements.txt

Write-Host "Backend API setup complete!" -ForegroundColor Green
Set-Location ".."

# Setup Backend Services
Write-Host "Setting up Backend Services (Node.js)..." -ForegroundColor Yellow
Set-Location "backend-services"

# Install dependencies
npm install

Write-Host "Backend Services setup complete!" -ForegroundColor Green
Set-Location ".."

# Setup Frontend
Write-Host "Setting up Frontend (Next.js)..." -ForegroundColor Yellow
Set-Location "frontend"

# Install dependencies
npm install

Write-Host "Frontend setup complete!" -ForegroundColor Green
Set-Location ".."

Write-Host "Setup completed successfully!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Start PostgreSQL database: docker-compose up postgres -d" -ForegroundColor White
Write-Host "2. Run database migrations: cd backend-api && python -m alembic upgrade head" -ForegroundColor White
Write-Host "3. Start all services: docker-compose up" -ForegroundColor White
Write-Host "4. Or run individual services:" -ForegroundColor White
Write-Host "   - Backend API: cd backend-api && python main.py" -ForegroundColor White
Write-Host "   - Backend Services: cd backend-services && npm run dev" -ForegroundColor White
Write-Host "   - Frontend: cd frontend && npm run dev" -ForegroundColor White
