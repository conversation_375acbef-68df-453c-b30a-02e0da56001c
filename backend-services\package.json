{"name": "ecommerce-backend-services", "version": "1.0.0", "description": "Node.js microservices for e-commerce platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "socket.io": "^4.7.4", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "redis": "^4.6.10", "pg": "^8.11.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "winston": "^3.11.0", "compression": "^1.7.4", "rate-limiter-flexible": "^3.0.8"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0"}, "keywords": ["nodejs", "express", "microservices", "ecommerce", "email", "file-upload", "real-time"], "author": "Your Name", "license": "MIT"}