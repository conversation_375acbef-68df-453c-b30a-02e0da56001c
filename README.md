# E-Commerce Platform

Hệ thống thương mại điện tử hiện đại được xây dựng với kiến trúc microservices, sử dụng Next.js, FastAPI, và Node.js.

## 🚀 Features

### Core Features

- ✅ **<PERSON><PERSON><PERSON><PERSON> lý sản phẩm** - CRUD operations, categories, variants, images
- ✅ **Giỏ hàng thông minh** - Persistent cart, guest checkout
- ✅ **Hệ thống đặt hàng** - Order management, status tracking
- ✅ **Authentication & Authorization** - JWT-based auth, role management
- ✅ **Payment Integration** - Stripe payment processing
- ✅ **Real-time Notifications** - WebSocket-based updates
- ✅ **Email System** - Welcome emails, order confirmations
- ✅ **File Upload** - Image processing, secure file handling
- ✅ **Admin Dashboard** - Product management, order tracking
- ✅ **Responsive Design** - Mobile-first approach

### Technical Features

- 🔒 **Security** - HTTPS, input validation, rate limiting
- 📊 **Monitoring** - Prometheus metrics, Grafana dashboards
- 🐳 **Containerization** - Docker & Docker Compose
- 🧪 **Testing** - Unit tests, integration tests
- 📚 **Documentation** - API docs, setup guides
- 🚀 **CI/CD** - GitHub Actions pipeline

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │ Backend Services│
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (Node.js)     │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 3001    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   Port: 5432    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │     Redis       │
                    │   Port: 6379    │
                    └─────────────────┘
```

### Technology Stack

**Frontend**

- Next.js 14 (React 18)
- TypeScript
- Tailwind CSS
- React Query
- Zustand (State Management)
- Socket.IO Client

**Backend API**

- FastAPI (Python)
- SQLAlchemy ORM
- PostgreSQL
- Redis
- JWT Authentication
- Pydantic Validation

**Backend Services**

- Node.js + Express
- Socket.IO
- Nodemailer
- Multer + Sharp
- Redis

**Infrastructure**

- Docker & Docker Compose
- Nginx (Reverse Proxy)
- Prometheus + Grafana
- GitHub Actions (CI/CD)

## 📁 Project Structure

```
ecommerce-platform/
├── frontend/              # Next.js React application
│   ├── src/
│   │   ├── app/          # App Router pages
│   │   ├── components/   # Reusable components
│   │   ├── lib/          # Utilities and API clients
│   │   ├── hooks/        # Custom React hooks
│   │   ├── store/        # State management
│   │   └── types/        # TypeScript definitions
│   ├── public/           # Static assets
│   └── package.json
├── backend-api/           # FastAPI application
│   ├── app/
│   │   ├── api/          # API routes
│   │   ├── core/         # Core functionality
│   │   ├── models/       # Database models
│   │   ├── schemas/      # Pydantic schemas
│   │   └── services/     # Business logic
│   ├── tests/            # Test files
│   └── requirements.txt
├── backend-services/      # Node.js microservices
│   ├── src/
│   │   ├── routes/       # Express routes
│   │   ├── services/     # Service layer
│   │   ├── middleware/   # Custom middleware
│   │   └── utils/        # Utilities
│   ├── tests/            # Test files
│   └── package.json
├── database/              # Database schemas and migrations
│   ├── init.sql          # Database schema
│   ├── seed.sql          # Sample data
│   └── README.md
├── docs/                  # Documentation
│   ├── SETUP.md          # Setup instructions
│   ├── API.md            # API documentation
│   ├── DEPLOYMENT.md     # Deployment guide
│   └── SECURITY.md       # Security guidelines
├── scripts/               # Utility scripts
│   ├── setup.ps1         # Windows setup script
│   └── start-dev.ps1     # Development startup
├── .github/workflows/     # CI/CD pipelines
├── docker-compose.yml     # Development environment
├── docker-compose.prod.yml # Production environment
└── README.md
```

## 🚀 Quick Start

### Prerequisites

- **Node.js 18+** - [Download](https://nodejs.org/)
- **Python 3.9+** - [Download](https://python.org/)
- **Docker & Docker Compose** - [Download](https://docker.com/)
- **PostgreSQL 14+** (or use Docker)

### Option 1: Docker Compose (Recommended)

```bash
# Clone repository
git clone <repository-url>
cd ecommerce-platform

# Copy environment file
cp .env.example .env

# Start all services
docker-compose up -d

# Load sample data
docker exec -i ecommerce_postgres psql -U postgres -d ecommerce_db < database/seed.sql
```

### Option 2: Manual Setup

```bash
# 1. Setup Backend API
cd backend-api
python -m venv venv
source venv/bin/activate  # Windows: .\venv\Scripts\Activate.ps1
pip install -r requirements.txt

# 2. Setup Backend Services
cd ../backend-services
npm install

# 3. Setup Frontend
cd ../frontend
npm install

# 4. Start PostgreSQL and Redis
docker-compose up postgres redis -d

# 5. Start services (in separate terminals)
cd backend-api && python main.py
cd backend-services && npm run dev
cd frontend && npm run dev
```

### Option 3: Windows Quick Setup

```powershell
# Run automated setup script
.\scripts\setup.ps1

# Start development environment
.\scripts\start-dev.ps1
```

## 🌐 Access Points

Once running, access the application at:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Backend Services**: http://localhost:3001

## 📖 Documentation

- **[Setup Guide](docs/SETUP.md)** - Detailed installation instructions
- **[API Documentation](docs/API.md)** - Complete API reference
- **[Deployment Guide](docs/DEPLOYMENT.md)** - Production deployment
- **[Security Guidelines](docs/SECURITY.md)** - Security best practices

## 🧪 Testing

```bash
# Backend API tests
cd backend-api
pytest tests/ -v

# Backend Services tests
cd backend-services
npm test

# Frontend tests
cd frontend
npm test

# Run all tests
npm run test:all
```

## 🔧 Development

### Environment Variables

Key environment variables to configure:

```bash
# Database
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/ecommerce_db

# Redis
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET_KEY=your-super-secret-key

# Email
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Payment
STRIPE_PUBLIC_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_key
```

### Database Schema

The platform uses PostgreSQL with the following main tables:

- `users` - User accounts and profiles
- `products` - Product catalog
- `categories` - Product categories (nested)
- `orders` - Order management
- `cart` - Shopping cart items
- `payments` - Payment transactions

See [database/README.md](database/README.md) for detailed schema information.

## 🚀 Deployment

### Production Deployment

```bash
# Using Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# Using Kubernetes
kubectl apply -f k8s/

# Cloud deployment (AWS/GCP/Azure)
# See docs/DEPLOYMENT.md for detailed instructions
```

### Environment-specific Configurations

- **Development**: `docker-compose.yml`
- **Production**: `docker-compose.prod.yml`
- **Kubernetes**: `k8s/` directory
- **Cloud**: Platform-specific configurations

## 📊 Monitoring

The platform includes built-in monitoring:

- **Prometheus** - Metrics collection
- **Grafana** - Metrics visualization
- **Health Checks** - Service health monitoring
- **Logging** - Centralized log management

Access monitoring at:

- Grafana: http://localhost:3001 (admin/admin)
- Prometheus: http://localhost:9090

## 🔒 Security

Security features implemented:

- JWT-based authentication
- Password hashing with bcrypt
- Input validation and sanitization
- Rate limiting
- CORS protection
- SQL injection prevention
- XSS protection
- HTTPS enforcement (production)

See [docs/SECURITY.md](docs/SECURITY.md) for detailed security guidelines.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow existing code style
- Write tests for new features
- Update documentation
- Ensure all tests pass
- Follow security best practices

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs/](docs/) directory
- **Issues**: Create a GitHub issue
- **Discussions**: Use GitHub Discussions
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [FastAPI](https://fastapi.tiangolo.com/) - Python web framework
- [PostgreSQL](https://postgresql.org/) - Database
- [Docker](https://docker.com/) - Containerization
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework

---

**Built with ❤️ by [Your Team Name]**
