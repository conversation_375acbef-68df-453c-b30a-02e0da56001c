# E-Commerce Platform Setup Guide

## Prerequisites

Before setting up the platform, ensure you have the following installed:

- **Node.js 18+** - [Download](https://nodejs.org/)
- **Python 3.9+** - [Download](https://python.org/)
- **PostgreSQL 14+** - [Download](https://postgresql.org/) or use Docker
- **Docker & Docker Compose** - [Download](https://docker.com/)
- **Git** - [Download](https://git-scm.com/)

## Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd ecommerce-platform

# Run setup script (Windows)
.\scripts\setup.ps1

# Or setup manually (see Manual Setup section)
```

### 2. Environment Configuration

Copy `.env.example` to `.env` and update the values:

```bash
cp .env.example .env
```

Key environment variables to configure:
- `DATABASE_URL` - PostgreSQL connection string
- `REDIS_URL` - Redis connection string
- `JWT_SECRET_KEY` - Secret key for JWT tokens
- `SMTP_*` - Email configuration for notifications
- `STRIPE_*` - Payment gateway configuration

### 3. Start Services

#### Option A: Using Docker (Recommended)

```bash
# Start all services
docker-compose up

# Or start in background
docker-compose up -d
```

#### Option B: Manual Development Setup

```bash
# Start database services
docker-compose up postgres redis -d

# Terminal 1: Backend API
cd backend-api
python -m venv venv
source venv/bin/activate  # On Windows: .\venv\Scripts\Activate.ps1
pip install -r requirements.txt
python main.py

# Terminal 2: Backend Services
cd backend-services
npm install
npm run dev

# Terminal 3: Frontend
cd frontend
npm install
npm run dev
```

#### Option C: Windows Development Script

```powershell
# Start all services in development mode
.\scripts\start-dev.ps1
```

### 4. Database Setup

```bash
# Run database migrations (if using Alembic)
cd backend-api
alembic upgrade head

# Load seed data
psql -d ecommerce_db -f ../database/seed.sql
```

## Service URLs

Once running, the services will be available at:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Backend Services**: http://localhost:3001
- **API Documentation**: http://localhost:8000/docs
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

## Manual Setup

### Backend API (FastAPI)

```bash
cd backend-api

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: .\venv\Scripts\Activate.ps1

# Install dependencies
pip install -r requirements.txt

# Run development server
python main.py
```

### Backend Services (Node.js)

```bash
cd backend-services

# Install dependencies
npm install

# Run development server
npm run dev
```

### Frontend (Next.js)

```bash
cd frontend

# Install dependencies
npm install

# Run development server
npm run dev
```

## Database Setup

### Using Docker

```bash
# Start PostgreSQL
docker-compose up postgres -d

# Create database and run migrations
docker exec -it ecommerce_postgres psql -U postgres -c "CREATE DATABASE ecommerce_db;"
```

### Manual PostgreSQL Setup

```bash
# Create database
createdb ecommerce_db

# Run schema
psql -d ecommerce_db -f database/init.sql

# Load seed data
psql -d ecommerce_db -f database/seed.sql
```

## Development

### Code Structure

```
├── frontend/           # Next.js React application
├── backend-api/        # FastAPI Python application
├── backend-services/   # Node.js microservices
├── database/          # Database schemas and migrations
├── docker/            # Docker configurations
├── docs/              # Documentation
└── scripts/           # Setup and deployment scripts
```

### API Documentation

- FastAPI auto-generated docs: http://localhost:8000/docs
- Redoc documentation: http://localhost:8000/redoc

### Testing

```bash
# Backend API tests
cd backend-api
pytest

# Backend Services tests
cd backend-services
npm test

# Frontend tests
cd frontend
npm test
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 3001, 8000, 5432, 6379 are available
2. **Database connection**: Check PostgreSQL is running and credentials are correct
3. **Node modules**: Delete `node_modules` and run `npm install` again
4. **Python dependencies**: Recreate virtual environment and reinstall packages

### Logs

```bash
# View all service logs
docker-compose logs

# View specific service logs
docker-compose logs frontend
docker-compose logs backend-api
docker-compose logs backend-services
```

### Reset Database

```bash
# Stop services
docker-compose down

# Remove database volume
docker volume rm ecommerce_postgres_data

# Restart services
docker-compose up
```

## Production Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for production deployment instructions.

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md) for development guidelines.
