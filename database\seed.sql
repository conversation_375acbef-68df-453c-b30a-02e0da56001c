-- Seed data for E-Commerce Database

-- Insert sample categories
INSERT INTO categories (id, name, slug, description, parent_id, is_active, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Electronics', 'electronics', 'Electronic devices and gadgets', NULL, TRUE, 1),
('550e8400-e29b-41d4-a716-446655440002', 'Clothing', 'clothing', 'Fashion and apparel', NULL, TRUE, 2),
('550e8400-e29b-41d4-a716-446655440003', 'Books', 'books', 'Books and literature', NULL, TRUE, 3),
('550e8400-e29b-41d4-a716-446655440004', 'Smartphones', 'smartphones', 'Mobile phones and accessories', '550e8400-e29b-41d4-a716-446655440001', TRUE, 1),
('550e8400-e29b-41d4-a716-446655440005', 'Laptops', 'laptops', 'Laptop computers', '550e8400-e29b-41d4-a716-446655440001', TRUE, 2),
('550e8400-e29b-41d4-a716-446655440006', 'Men''s Clothing', 'mens-clothing', 'Clothing for men', '550e8400-e29b-41d4-a716-446655440002', TRUE, 1),
('550e8400-e29b-41d4-a716-446655440007', 'Women''s Clothing', 'womens-clothing', 'Clothing for women', '550e8400-e29b-41d4-a716-446655440002', TRUE, 2);

-- Insert sample products
INSERT INTO products (id, name, slug, description, short_description, sku, price, compare_price, stock_quantity, is_active, is_featured) VALUES
('660e8400-e29b-41d4-a716-446655440001', 'iPhone 15 Pro', 'iphone-15-pro', 'Latest iPhone with advanced features and powerful performance', 'Latest iPhone with A17 Pro chip', 'IPH15PRO001', 999.00, 1099.00, 50, TRUE, TRUE),
('660e8400-e29b-41d4-a716-446655440002', 'MacBook Air M2', 'macbook-air-m2', 'Lightweight laptop with M2 chip for exceptional performance', 'MacBook Air with M2 chip', 'MBA-M2-001', 1199.00, 1299.00, 30, TRUE, TRUE),
('660e8400-e29b-41d4-a716-446655440003', 'Samsung Galaxy S24', 'samsung-galaxy-s24', 'Premium Android smartphone with AI features', 'Galaxy S24 with AI capabilities', 'SGS24-001', 799.00, 899.00, 40, TRUE, FALSE),
('660e8400-e29b-41d4-a716-446655440004', 'Men''s Cotton T-Shirt', 'mens-cotton-tshirt', 'Comfortable cotton t-shirt for everyday wear', 'Comfortable cotton t-shirt', 'MCT-001', 29.99, 39.99, 100, TRUE, FALSE),
('660e8400-e29b-41d4-a716-446655440005', 'Women''s Summer Dress', 'womens-summer-dress', 'Elegant summer dress perfect for any occasion', 'Elegant summer dress', 'WSD-001', 79.99, 99.99, 75, TRUE, TRUE),
('660e8400-e29b-41d4-a716-446655440006', 'Programming Book: Clean Code', 'clean-code-book', 'Essential book for software developers', 'Clean Code by Robert Martin', 'BOOK-CC-001', 45.99, 55.99, 200, TRUE, FALSE);

-- Link products to categories
INSERT INTO product_categories (product_id, category_id) VALUES
('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440004'), -- iPhone -> Smartphones
('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440005'), -- MacBook -> Laptops
('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440004'), -- Galaxy -> Smartphones
('660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440006'), -- T-shirt -> Men's Clothing
('660e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440007'), -- Dress -> Women's Clothing
('660e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440003'); -- Book -> Books

-- Insert sample product variants
INSERT INTO product_variants (id, product_id, name, sku, price, stock_quantity, attributes) VALUES
('770e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'iPhone 15 Pro 128GB Natural Titanium', 'IPH15PRO-128-NT', 999.00, 25, '{"storage": "128GB", "color": "Natural Titanium"}'),
('770e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440001', 'iPhone 15 Pro 256GB Blue Titanium', 'IPH15PRO-256-BT', 1099.00, 15, '{"storage": "256GB", "color": "Blue Titanium"}'),
('770e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440004', 'Men''s T-Shirt Size M Blue', 'MCT-M-BLUE', 29.99, 30, '{"size": "M", "color": "Blue"}'),
('770e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440004', 'Men''s T-Shirt Size L Red', 'MCT-L-RED', 29.99, 25, '{"size": "L", "color": "Red"}'),
('770e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440005', 'Summer Dress Size S Floral', 'WSD-S-FLORAL', 79.99, 20, '{"size": "S", "pattern": "Floral"}'),
('770e8400-e29b-41d4-a716-446655440006', '660e8400-e29b-41d4-a716-446655440005', 'Summer Dress Size M Solid', 'WSD-M-SOLID', 79.99, 30, '{"size": "M", "pattern": "Solid"}');

-- Insert sample users (passwords are hashed for 'password123')
INSERT INTO users (id, email, password_hash, first_name, last_name, phone, is_admin) VALUES
('880e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'Admin', 'User', '+1234567890', TRUE),
('880e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'John', 'Doe', '+1234567891', FALSE),
('880e8400-e29b-41d4-a716-446655440003', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'Jane', 'Smith', '+1234567892', FALSE);

-- Insert sample user addresses
INSERT INTO user_addresses (user_id, address_line1, city, state, postal_code, country, is_default) VALUES
('880e8400-e29b-41d4-a716-446655440002', '123 Main St', 'New York', 'NY', '10001', 'USA', TRUE),
('880e8400-e29b-41d4-a716-446655440003', '456 Oak Ave', 'Los Angeles', 'CA', '90210', 'USA', TRUE);

-- Insert sample coupons
INSERT INTO coupons (code, name, description, discount_type, discount_value, minimum_amount, usage_limit, is_active, expires_at) VALUES
('WELCOME10', 'Welcome Discount', '10% off for new customers', 'percentage', 10.00, 50.00, 100, TRUE, '2025-12-31 23:59:59'),
('SAVE20', 'Save $20', '$20 off orders over $100', 'fixed_amount', 20.00, 100.00, 50, TRUE, '2025-12-31 23:59:59'),
('SUMMER15', 'Summer Sale', '15% off summer items', 'percentage', 15.00, 30.00, 200, TRUE, '2025-09-30 23:59:59');

-- Insert sample product images
INSERT INTO product_images (product_id, image_url, alt_text, sort_order, is_primary) VALUES
('660e8400-e29b-41d4-a716-446655440001', '/images/products/iphone-15-pro-1.jpg', 'iPhone 15 Pro front view', 1, TRUE),
('660e8400-e29b-41d4-a716-446655440001', '/images/products/iphone-15-pro-2.jpg', 'iPhone 15 Pro back view', 2, FALSE),
('660e8400-e29b-41d4-a716-446655440002', '/images/products/macbook-air-m2-1.jpg', 'MacBook Air M2 open', 1, TRUE),
('660e8400-e29b-41d4-a716-446655440003', '/images/products/galaxy-s24-1.jpg', 'Samsung Galaxy S24', 1, TRUE),
('660e8400-e29b-41d4-a716-446655440004', '/images/products/mens-tshirt-1.jpg', 'Men''s Cotton T-Shirt', 1, TRUE),
('660e8400-e29b-41d4-a716-446655440005', '/images/products/womens-dress-1.jpg', 'Women''s Summer Dress', 1, TRUE),
('660e8400-e29b-41d4-a716-446655440006', '/images/products/clean-code-book-1.jpg', 'Clean Code Book Cover', 1, TRUE);
