const nodemailer = require('nodemailer');
const config = require('../config');
const logger = require('../utils/logger');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.secure,
      auth: config.email.auth
    });
  }

  async sendEmail({ to, subject, html, text }) {
    try {
      const mailOptions = {
        from: config.email.auth.user,
        to,
        subject,
        html,
        text
      };

      const result = await this.transporter.sendMail(mailOptions);
      logger.info(`Email sent successfully to ${to}`, { messageId: result.messageId });
      return { success: true, messageId: result.messageId };
    } catch (error) {
      logger.error('Failed to send email', { error: error.message, to, subject });
      throw error;
    }
  }

  async sendWelcomeEmail(userEmail, userName) {
    const subject = 'Welcome to Our E-Commerce Store!';
    const html = `
      <h1>Welcome ${userName}!</h1>
      <p>Thank you for joining our e-commerce platform.</p>
      <p>We're excited to have you as part of our community.</p>
      <p>Happy shopping!</p>
    `;
    const text = `Welcome ${userName}! Thank you for joining our e-commerce platform.`;

    return this.sendEmail({ to: userEmail, subject, html, text });
  }

  async sendOrderConfirmation(userEmail, orderData) {
    const subject = `Order Confirmation - #${orderData.orderNumber}`;
    const html = `
      <h1>Order Confirmation</h1>
      <p>Thank you for your order!</p>
      <p><strong>Order Number:</strong> ${orderData.orderNumber}</p>
      <p><strong>Total Amount:</strong> $${orderData.totalAmount}</p>
      <p>We'll send you another email when your order ships.</p>
    `;
    const text = `Order Confirmation - #${orderData.orderNumber}. Total: $${orderData.totalAmount}`;

    return this.sendEmail({ to: userEmail, subject, html, text });
  }

  async sendPasswordReset(userEmail, resetToken) {
    const subject = 'Password Reset Request';
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    const html = `
      <h1>Password Reset</h1>
      <p>You requested a password reset.</p>
      <p>Click the link below to reset your password:</p>
      <a href="${resetUrl}">Reset Password</a>
      <p>This link will expire in 1 hour.</p>
      <p>If you didn't request this, please ignore this email.</p>
    `;
    const text = `Password reset link: ${resetUrl}`;

    return this.sendEmail({ to: userEmail, subject, html, text });
  }
}

module.exports = new EmailService();
