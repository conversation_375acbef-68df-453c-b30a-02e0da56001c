from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON>ole<PERSON>, DateTime, Foreign<PERSON>ey, Integer, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from ..core.database import Base


class Review(Base):
    __tablename__ = "reviews"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    order_item_id = Column(UUID(as_uuid=True), ForeignKey("order_items.id", ondelete="SET NULL"))
    rating = Column(Integer, nullable=False)  # 1-5 stars
    title = Column(String(255))
    comment = Column(Text)
    is_verified = Column(Boolean, default=False)  # Verified purchase
    is_approved = Column(Boolean, default=False)  # Admin approved
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Unique constraint to prevent multiple reviews for same product by same user for same order item
    __table_args__ = (
        UniqueConstraint('product_id', 'user_id', 'order_item_id', name='unique_product_user_order_review'),
    )

    # Relationships
    product = relationship("Product", back_populates="reviews")
    user = relationship("User", back_populates="reviews")

    @property
    def is_valid_rating(self):
        return 1 <= self.rating <= 5
