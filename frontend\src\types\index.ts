export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  is_active: boolean
  is_admin: boolean
  created_at: string
  updated_at: string
}

export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  parent_id?: string
  image_url?: string
  sort_order: number
  created_at: string
  updated_at: string
  children?: Category[]
}

export interface ProductImage {
  id: string
  image_url: string
  alt_text?: string
  is_primary: boolean
}

export interface ProductVariant {
  id: string
  name: string
  sku: string
  price?: number
  stock_quantity: number
  attributes: Record<string, any>
}

export interface Product {
  id: string
  name: string
  slug: string
  description?: string
  short_description?: string
  sku: string
  price: number
  compare_price?: number
  stock_quantity: number
  is_active: boolean
  is_featured: boolean
  created_at: string
  updated_at: string
  categories?: Category[]
  images?: ProductImage[]
  variants?: ProductVariant[]
}

export interface CartItem {
  id: string
  product_id: string
  variant_id?: string
  quantity: number
  price: number
  product: Product
  variant?: ProductVariant
}

export interface Cart {
  id: string
  user_id?: string
  session_id?: string
  items: CartItem[]
  created_at: string
  updated_at: string
}

export interface OrderItem {
  id: string
  product_id: string
  variant_id?: string
  product_name: string
  product_sku: string
  quantity: number
  unit_price: number
  total_price: number
}

export interface Order {
  id: string
  user_id?: string
  order_number: string
  status: string
  total_amount: number
  subtotal: number
  tax_amount: number
  shipping_amount: number
  discount_amount: number
  currency: string
  billing_first_name: string
  billing_last_name: string
  billing_email: string
  billing_phone?: string
  billing_address_line1: string
  billing_address_line2?: string
  billing_city: string
  billing_state: string
  billing_postal_code: string
  billing_country: string
  shipping_first_name: string
  shipping_last_name: string
  shipping_address_line1: string
  shipping_address_line2?: string
  shipping_city: string
  shipping_state: string
  shipping_postal_code: string
  shipping_country: string
  notes?: string
  created_at: string
  updated_at: string
  items: OrderItem[]
}

export interface AuthResponse {
  access_token: string
  token_type: string
}

export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  has_next: boolean
  has_prev: boolean
}
