# API Documentation

## Overview

The E-Commerce platform consists of two main API services:

1. **Backend API (FastAPI)** - Main business logic and data operations
2. **Backend Services (Node.js)** - Microservices for email, file upload, and real-time features

## Backend API (FastAPI)

Base URL: `http://localhost:8000/api/v1`

### Authentication

All protected endpoints require a Bearer token in the Authorization header:

```
Authorization: Bearer <access_token>
```

#### Login
```http
POST /auth/login
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=password123
```

#### Register
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "John",
  "last_name": "<PERSON><PERSON>",
  "phone": "+1234567890"
}
```

#### Get Current User
```http
GET /auth/me
Authorization: Bearer <token>
```

### Products

#### Get All Products
```http
GET /products?skip=0&limit=10&category_id=<uuid>&search=<query>&is_featured=true
```

#### Get Product by ID
```http
GET /products/{product_id}
```

### Categories

#### Get All Categories
```http
GET /categories
```

#### Get Category by ID
```http
GET /categories/{category_id}
```

### Cart

#### Get User Cart
```http
GET /cart
Authorization: Bearer <token>
```

#### Add Item to Cart
```http
POST /cart/items
Authorization: Bearer <token>
Content-Type: application/json

{
  "product_id": "<uuid>",
  "variant_id": "<uuid>",
  "quantity": 2
}
```

#### Update Cart Item
```http
PUT /cart/items/{item_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "quantity": 3
}
```

#### Remove Cart Item
```http
DELETE /cart/items/{item_id}
Authorization: Bearer <token>
```

### Orders

#### Get User Orders
```http
GET /orders
Authorization: Bearer <token>
```

#### Get Order by ID
```http
GET /orders/{order_id}
Authorization: Bearer <token>
```

#### Create Order
```http
POST /orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "billing_address": {
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address_line1": "123 Main St",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "USA"
  },
  "shipping_address": {
    // Same structure as billing_address
  },
  "payment_method": "stripe",
  "notes": "Please deliver after 5 PM"
}
```

### Users

#### Get User Profile
```http
GET /users/me
Authorization: Bearer <token>
```

#### Update User Profile
```http
PUT /users/me
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890"
}
```

## Backend Services (Node.js)

Base URL: `http://localhost:3001/api`

### Email Service

#### Send Welcome Email
```http
POST /email/welcome
Content-Type: application/json

{
  "email": "<EMAIL>",
  "name": "John Doe"
}
```

#### Send Order Confirmation
```http
POST /email/order-confirmation
Content-Type: application/json

{
  "email": "<EMAIL>",
  "orderData": {
    "orderNumber": "ORD-001",
    "totalAmount": 99.99
  }
}
```

#### Send Custom Email
```http
POST /email/send
Content-Type: application/json

{
  "to": "<EMAIL>",
  "subject": "Custom Subject",
  "html": "<h1>Hello World</h1>",
  "text": "Hello World"
}
```

### File Upload Service

#### Upload Single File
```http
POST /upload/single
Content-Type: multipart/form-data

file: <file>
width: 800
height: 600
quality: 80
subfolder: products
```

#### Upload Multiple Files
```http
POST /upload/multiple
Content-Type: multipart/form-data

files: <file1>
files: <file2>
subfolder: gallery
```

#### Delete File
```http
DELETE /upload/{filename}?subfolder=products
```

### Real-time Notifications

#### Send Notification to Room
```http
POST /notifications/send
Content-Type: application/json

{
  "room": "user_123",
  "event": "order_status_update",
  "data": {
    "orderId": "order_456",
    "status": "shipped"
  }
}
```

#### Broadcast Notification
```http
POST /notifications/broadcast
Content-Type: application/json

{
  "event": "system_maintenance",
  "data": {
    "message": "System will be down for maintenance at 2 AM"
  }
}
```

#### Get Connected Clients Count
```http
GET /notifications/clients
```

## WebSocket Events

Connect to: `ws://localhost:3001`

### Client Events

#### Join Room
```javascript
socket.emit('join_room', 'user_123')
```

#### Leave Room
```javascript
socket.emit('leave_room', 'user_123')
```

### Server Events

#### Order Status Update
```javascript
socket.on('order_status_update', (data) => {
  console.log('Order status:', data.status)
})
```

#### New Message
```javascript
socket.on('new_message', (data) => {
  console.log('New message:', data.message)
})
```

## Error Responses

All APIs return errors in the following format:

```json
{
  "error": "Error message",
  "detail": "Detailed error description",
  "status_code": 400
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

- Backend Services: 100 requests per minute per IP
- File uploads: 10MB max file size
- Email service: 50 emails per hour per IP

## Interactive Documentation

- FastAPI Swagger UI: http://localhost:8000/docs
- FastAPI ReDoc: http://localhost:8000/redoc
