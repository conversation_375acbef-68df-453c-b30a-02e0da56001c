name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test-backend-api:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      working-directory: ./backend-api
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run tests
      working-directory: ./backend-api
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
      run: |
        pytest tests/ -v
    
    - name: Run linting
      working-directory: ./backend-api
      run: |
        pip install flake8
        flake8 app/ --count --select=E9,F63,F7,F82 --show-source --statistics

  test-backend-services:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: backend-services/package-lock.json
    
    - name: Install dependencies
      working-directory: ./backend-services
      run: npm ci
    
    - name: Run tests
      working-directory: ./backend-services
      run: npm test
    
    - name: Run linting
      working-directory: ./backend-services
      run: npm run lint

  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run tests
      working-directory: ./frontend
      run: npm test
    
    - name: Run type checking
      working-directory: ./frontend
      run: npm run type-check
    
    - name: Run linting
      working-directory: ./frontend
      run: npm run lint
    
    - name: Build application
      working-directory: ./frontend
      run: npm run build

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  build-and-push:
    needs: [test-backend-api, test-backend-services, test-frontend]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push Backend API
      uses: docker/build-push-action@v4
      with:
        context: ./backend-api
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/ecommerce-backend-api:latest
    
    - name: Build and push Backend Services
      uses: docker/build-push-action@v4
      with:
        context: ./backend-services
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/ecommerce-backend-services:latest
    
    - name: Build and push Frontend
      uses: docker/build-push-action@v4
      with:
        context: ./frontend
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/ecommerce-frontend:latest
