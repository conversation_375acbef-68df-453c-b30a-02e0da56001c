'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useQuery } from 'react-query'
import { api } from '@/lib/api'
import { Product } from '@/types'

export function FeaturedProducts() {
  const { data: products, isLoading, error } = useQuery<Product[]>(
    'featured-products',
    () => api.products.getFeatured().then(res => res.data),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  )

  if (isLoading) {
    return (
      <section className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Products</h2>
          <p className="text-gray-600">Check out our most popular items</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-64 mb-4"></div>
              <div className="bg-gray-200 h-4 rounded mb-2"></div>
              <div className="bg-gray-200 h-3 rounded w-2/3 mb-2"></div>
              <div className="bg-gray-200 h-4 rounded w-1/3"></div>
            </div>
          ))}
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="py-16">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Products</h2>
          <p className="text-red-600">Failed to load products. Please try again later.</p>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Products</h2>
        <p className="text-gray-600">Check out our most popular items</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {products?.slice(0, 8).map((product) => (
          <Link
            key={product.id}
            href={`/products/${product.slug}`}
            className="group block bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow border border-gray-200"
          >
            <div className="aspect-square relative overflow-hidden rounded-t-lg">
              {product.images && product.images.length > 0 ? (
                <Image
                  src={product.images[0].image_url}
                  alt={product.images[0].alt_text || product.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400">No Image</span>
                </div>
              )}
              {product.compare_price && product.compare_price > product.price && (
                <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold">
                  Sale
                </div>
              )}
            </div>
            
            <div className="p-4">
              <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{product.name}</h3>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.short_description}</p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold text-gray-900">${product.price}</span>
                  {product.compare_price && product.compare_price > product.price && (
                    <span className="text-sm text-gray-500 line-through">${product.compare_price}</span>
                  )}
                </div>
                
                <div className="text-sm text-gray-500">
                  {product.stock_quantity > 0 ? (
                    <span className="text-green-600">In Stock</span>
                  ) : (
                    <span className="text-red-600">Out of Stock</span>
                  )}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
      
      <div className="text-center mt-8">
        <Link
          href="/products"
          className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          View All Products
        </Link>
      </div>
    </section>
  )
}
