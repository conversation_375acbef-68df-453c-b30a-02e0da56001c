import { render, screen, fireEvent } from '@testing-library/react'
import { Header } from '../layout/Header'

// Mock Next.js router
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => {
    return <a href={href}>{children}</a>
  }
})

describe('Header', () => {
  it('renders the logo and navigation', () => {
    render(<Header />)
    
    // Check if logo is present
    expect(screen.getByText('E-Commerce')).toBeInTheDocument()
    
    // Check if navigation links are present
    expect(screen.getByText('Home')).toBeInTheDocument()
    expect(screen.getByText('Products')).toBeInTheDocument()
    expect(screen.getByText('Categories')).toBeInTheDocument()
    expect(screen.getByText('About')).toBeInTheDocument()
    expect(screen.getByText('Contact')).toBeInTheDocument()
  })

  it('shows search input on desktop', () => {
    render(<Header />)
    
    const searchInput = screen.getByPlaceholderText('Search products...')
    expect(searchInput).toBeInTheDocument()
  })

  it('toggles mobile menu when hamburger button is clicked', () => {
    render(<Header />)
    
    // Mobile menu should not be visible initially
    const mobileNav = screen.queryByRole('navigation')
    
    // Click hamburger button
    const hamburgerButton = screen.getByRole('button')
    fireEvent.click(hamburgerButton)
    
    // Mobile navigation should now be visible
    // Note: This test might need adjustment based on actual implementation
  })

  it('displays cart icon with item count', () => {
    render(<Header />)
    
    // Check if cart icon is present
    const cartLink = screen.getByRole('link', { name: /cart/i })
    expect(cartLink).toBeInTheDocument()
    
    // Check if cart count is displayed (should be 0 initially)
    expect(screen.getByText('0')).toBeInTheDocument()
  })

  it('displays user icon', () => {
    render(<Header />)
    
    // Check if user icon/link is present
    const userLink = screen.getByRole('link', { name: /user/i })
    expect(userLink).toBeInTheDocument()
  })
})
