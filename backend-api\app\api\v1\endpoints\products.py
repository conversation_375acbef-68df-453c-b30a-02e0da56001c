from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ....core.database import get_db
from ....models.product import Product, Category

router = APIRouter()


@router.get("/")
def get_products(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    category_id: Optional[str] = None,
    search: Optional[str] = None,
    is_featured: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    query = db.query(Product).filter(Product.is_active == True)
    
    if category_id:
        query = query.join(Product.categories).filter(Category.id == category_id)
    
    if search:
        query = query.filter(Product.name.ilike(f"%{search}%"))
    
    if is_featured is not None:
        query = query.filter(Product.is_featured == is_featured)
    
    products = query.offset(skip).limit(limit).all()
    
    return [
        {
            "id": str(product.id),
            "name": product.name,
            "slug": product.slug,
            "description": product.description,
            "short_description": product.short_description,
            "sku": product.sku,
            "price": float(product.price),
            "compare_price": float(product.compare_price) if product.compare_price else None,
            "stock_quantity": product.stock_quantity,
            "is_active": product.is_active,
            "is_featured": product.is_featured,
            "created_at": product.created_at,
            "updated_at": product.updated_at
        }
        for product in products
    ]


@router.get("/{product_id}")
def get_product(product_id: str, db: Session = Depends(get_db)):
    product = db.query(Product).filter(Product.id == product_id, Product.is_active == True).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    
    return {
        "id": str(product.id),
        "name": product.name,
        "slug": product.slug,
        "description": product.description,
        "short_description": product.short_description,
        "sku": product.sku,
        "price": float(product.price),
        "compare_price": float(product.compare_price) if product.compare_price else None,
        "stock_quantity": product.stock_quantity,
        "is_active": product.is_active,
        "is_featured": product.is_featured,
        "created_at": product.created_at,
        "updated_at": product.updated_at,
        "categories": [
            {
                "id": str(cat.id),
                "name": cat.name,
                "slug": cat.slug
            }
            for cat in product.categories
        ],
        "images": [
            {
                "id": str(img.id),
                "image_url": img.image_url,
                "alt_text": img.alt_text,
                "is_primary": img.is_primary
            }
            for img in product.images
        ],
        "variants": [
            {
                "id": str(var.id),
                "name": var.name,
                "sku": var.sku,
                "price": float(var.price) if var.price else None,
                "stock_quantity": var.stock_quantity,
                "attributes": var.attributes
            }
            for var in product.variants
        ]
    }
