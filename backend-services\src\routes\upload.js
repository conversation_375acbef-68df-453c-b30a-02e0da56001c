const express = require('express');
const uploadService = require('../services/uploadService');
const logger = require('../utils/logger');

const router = express.Router();

// Single file upload
router.post('/single', uploadService.getUploadMiddleware(), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { buffer, originalname, mimetype } = req.file;
    const { width, height, quality, subfolder = 'general' } = req.body;

    // Process image if it's an image
    let processedBuffer = buffer;
    if (mimetype.startsWith('image/')) {
      const options = {};
      if (width) options.width = parseInt(width);
      if (height) options.height = parseInt(height);
      if (quality) options.quality = parseInt(quality);

      processedBuffer = await uploadService.processImage(buffer, options);
    }

    // Generate filename and save
    const filename = uploadService.generateFilename(originalname, 'img_');
    const result = await uploadService.saveFile(processedBuffer, filename, subfolder);

    res.json({
      success: true,
      file: {
        ...result,
        originalName: originalname,
        mimetype,
        size: processedBuffer.length
      }
    });
  } catch (err) {
    logger.error('File upload failed', { error: err.message });
    res.status(500).json({ error: 'File upload failed' });
  }
});

// Multiple files upload
router.post('/multiple', uploadService.getMultipleUploadMiddleware(), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    const { subfolder = 'general' } = req.body;
    const uploadedFiles = [];

    for (const file of req.files) {
      const { buffer, originalname, mimetype } = file;

      // Process image if it's an image
      let processedBuffer = buffer;
      if (mimetype.startsWith('image/')) {
        processedBuffer = await uploadService.processImage(buffer);
      }

      // Generate filename and save
      const filename = uploadService.generateFilename(originalname, 'img_');
      const result = await uploadService.saveFile(processedBuffer, filename, subfolder);

      uploadedFiles.push({
        ...result,
        originalName: originalname,
        mimetype,
        size: processedBuffer.length
      });
    }

    res.json({
      success: true,
      files: uploadedFiles
    });
  } catch (err) {
    logger.error('Multiple file upload failed', { error: err.message });
    res.status(500).json({ error: 'File upload failed' });
  }
});

// Delete file
router.delete('/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const { subfolder = 'general' } = req.query;

    const filePath = subfolder ? `${subfolder}/${filename}` : filename;
    await uploadService.deleteFile(filePath);

    res.json({ success: true, message: 'File deleted successfully' });
  } catch (err) {
    logger.error('File deletion failed', { error: err.message });
    res.status(500).json({ error: 'File deletion failed' });
  }
});

module.exports = router;
