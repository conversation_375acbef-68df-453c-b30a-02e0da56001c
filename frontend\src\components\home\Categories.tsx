'use client'

import Link from 'next/link'
import { useQuery } from 'react-query'
import { api } from '@/lib/api'
import { Category } from '@/types'

export function Categories() {
  const { data: categories, isLoading, error } = useQuery<Category[]>(
    'categories',
    () => api.categories.getAll().then(res => res.data),
    {
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  )

  if (isLoading) {
    return (
      <section className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
          <p className="text-gray-600">Explore our wide range of product categories</p>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-32 mb-4"></div>
              <div className="bg-gray-200 h-4 rounded mb-2"></div>
              <div className="bg-gray-200 h-3 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="py-16">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
          <p className="text-red-600">Failed to load categories. Please try again later.</p>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
        <p className="text-gray-600">Explore our wide range of product categories</p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {categories?.slice(0, 8).map((category) => (
          <Link
            key={category.id}
            href={`/categories/${category.slug}`}
            className="group block"
          >
            <div className="bg-gray-100 rounded-lg p-8 text-center hover:bg-primary-50 transition-colors group-hover:shadow-lg">
              <div className="w-16 h-16 bg-primary-100 rounded-full mx-auto mb-4 flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                <span className="text-2xl font-bold text-primary-600">
                  {category.name.charAt(0)}
                </span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{category.name}</h3>
              <p className="text-sm text-gray-600">{category.description}</p>
            </div>
          </Link>
        ))}
      </div>
      
      <div className="text-center mt-8">
        <Link
          href="/categories"
          className="inline-flex items-center px-6 py-3 border border-primary-600 text-primary-600 rounded-lg hover:bg-primary-600 hover:text-white transition-colors"
        >
          View All Categories
        </Link>
      </div>
    </section>
  )
}
