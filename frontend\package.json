{"name": "ecommerce-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.0.3", "react": "^18", "react-dom": "^18", "@next/font": "14.0.3", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "socket.io-client": "^4.7.4", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "swiper": "^11.0.5", "framer-motion": "^10.16.16", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "js-cookie": "^3.0.5", "react-intersection-observer": "^9.5.3"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/js-cookie": "^3.0.6", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "tailwindcss": "^3.3.0"}}