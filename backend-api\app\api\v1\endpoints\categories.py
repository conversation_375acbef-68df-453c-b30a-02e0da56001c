from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ....core.database import get_db
from ....models.product import Category

router = APIRouter()


@router.get("/")
def get_categories(db: Session = Depends(get_db)):
    categories = db.query(Category).filter(Category.is_active == True).order_by(Category.sort_order).all()
    
    return [
        {
            "id": str(category.id),
            "name": category.name,
            "slug": category.slug,
            "description": category.description,
            "parent_id": str(category.parent_id) if category.parent_id else None,
            "image_url": category.image_url,
            "sort_order": category.sort_order,
            "created_at": category.created_at,
            "updated_at": category.updated_at
        }
        for category in categories
    ]


@router.get("/{category_id}")
def get_category(category_id: str, db: Session = Depends(get_db)):
    category = db.query(Category).filter(Category.id == category_id, Category.is_active == True).first()
    if not category:
        raise HTTPException(status_code=404, detail="Category not found")
    
    return {
        "id": str(category.id),
        "name": category.name,
        "slug": category.slug,
        "description": category.description,
        "parent_id": str(category.parent_id) if category.parent_id else None,
        "image_url": category.image_url,
        "sort_order": category.sort_order,
        "created_at": category.created_at,
        "updated_at": category.updated_at,
        "children": [
            {
                "id": str(child.id),
                "name": child.name,
                "slug": child.slug
            }
            for child in category.children if child.is_active
        ]
    }
