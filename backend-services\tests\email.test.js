const request = require('supertest');
const app = require('../src/index');

describe('Email Service', () => {
  describe('POST /api/email/send', () => {
    it('should send email successfully', async () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>',
        text: 'Test'
      };

      const response = await request(app)
        .post('/api/email/send')
        .send(emailData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.messageId).toBeDefined();
    });

    it('should return error for invalid email', async () => {
      const emailData = {
        to: 'invalid-email',
        subject: 'Test Email',
        html: '<h1>Test</h1>'
      };

      const response = await request(app)
        .post('/api/email/send')
        .send(emailData)
        .expect(400);

      expect(response.body.error).toBeDefined();
    });

    it('should return error for missing required fields', async () => {
      const emailData = {
        to: '<EMAIL>'
        // Missing subject and html
      };

      const response = await request(app)
        .post('/api/email/send')
        .send(emailData)
        .expect(400);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('POST /api/email/welcome', () => {
    it('should send welcome email successfully', async () => {
      const welcomeData = {
        email: '<EMAIL>',
        name: 'Test User'
      };

      const response = await request(app)
        .post('/api/email/welcome')
        .send(welcomeData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.messageId).toBeDefined();
    });
  });

  describe('POST /api/email/order-confirmation', () => {
    it('should send order confirmation email successfully', async () => {
      const orderData = {
        email: '<EMAIL>',
        orderData: {
          orderNumber: 'ORD-001',
          totalAmount: 99.99
        }
      };

      const response = await request(app)
        .post('/api/email/order-confirmation')
        .send(orderData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.messageId).toBeDefined();
    });
  });
});
