# Deployment Guide

## Overview

This guide covers deploying the E-Commerce platform to production environments using Docker, cloud services, and various deployment strategies.

## Prerequisites

- <PERSON>er and Docker Compose
- Domain name with DNS configured
- SSL certificate (Let's Encrypt recommended)
- Cloud provider account (AWS, GCP, Azure, or DigitalOcean)
- Database backup strategy

## Environment Setup

### 1. Production Environment Variables

Create a `.env.prod` file:

```bash
# Database
DB_NAME=ecommerce_prod
DB_USER=ecommerce_user
DB_PASSWORD=secure_password_here
DATABASE_URL=**************************************************************/ecommerce_prod

# Redis
REDIS_PASSWORD=redis_secure_password

# Security
JWT_SECRET_KEY=your_super_secure_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Domain
DOMAIN=yourdomain.com

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Payment
STRIPE_PUBLIC_KEY=pk_live_your_stripe_public_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key

# Monitoring
GRAFANA_PASSWORD=grafana_admin_password

# SSL
SSL_EMAIL=<EMAIL>
```

### 2. SSL Certificate Setup

#### Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt-get update
sudo apt-get install certbot

# Generate certificate
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ./nginx/ssl/
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ./nginx/ssl/
```

## Deployment Options

### Option 1: Docker Compose (Recommended for small to medium scale)

```bash
# Clone repository
git clone <repository-url>
cd ecommerce-platform

# Setup environment
cp .env.example .env.prod
# Edit .env.prod with production values

# Build and start services
docker-compose -f docker-compose.prod.yml up -d

# Run database migrations
docker exec ecommerce_backend_api_prod alembic upgrade head

# Load initial data
docker exec -i ecommerce_postgres_prod psql -U ecommerce_user -d ecommerce_prod < database/seed.sql
```

### Option 2: Kubernetes Deployment

#### Prerequisites
- Kubernetes cluster (EKS, GKE, AKS, or self-managed)
- kubectl configured
- Helm (optional but recommended)

```bash
# Create namespace
kubectl create namespace ecommerce

# Create secrets
kubectl create secret generic ecommerce-secrets \
  --from-env-file=.env.prod \
  --namespace=ecommerce

# Deploy using Kubernetes manifests
kubectl apply -f k8s/ --namespace=ecommerce

# Or using Helm
helm install ecommerce ./helm-chart --namespace=ecommerce
```

### Option 3: Cloud-Specific Deployments

#### AWS Deployment

```bash
# Using AWS ECS with Fargate
aws ecs create-cluster --cluster-name ecommerce-cluster

# Deploy using AWS CDK or CloudFormation
cdk deploy --all

# Or using AWS App Runner for simpler deployment
aws apprunner create-service --cli-input-json file://apprunner-config.json
```

#### Google Cloud Platform

```bash
# Using Google Cloud Run
gcloud run deploy ecommerce-frontend --source ./frontend --region us-central1
gcloud run deploy ecommerce-backend --source ./backend-api --region us-central1
gcloud run deploy ecommerce-services --source ./backend-services --region us-central1
```

#### Azure Deployment

```bash
# Using Azure Container Instances
az container create --resource-group ecommerce-rg --file docker-compose.prod.yml
```

## Database Setup

### Production Database Configuration

```sql
-- Create production database
CREATE DATABASE ecommerce_prod;
CREATE USER ecommerce_user WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE ecommerce_prod TO ecommerce_user;

-- Enable required extensions
\c ecommerce_prod
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
```

### Database Migration

```bash
# Run migrations
docker exec ecommerce_backend_api_prod alembic upgrade head

# Backup database
docker exec ecommerce_postgres_prod pg_dump -U ecommerce_user ecommerce_prod > backup.sql

# Restore database
docker exec -i ecommerce_postgres_prod psql -U ecommerce_user ecommerce_prod < backup.sql
```

## Monitoring and Logging

### Application Monitoring

```bash
# Access Grafana dashboard
http://yourdomain.com:3001
# Login: admin / [GRAFANA_PASSWORD]

# Access Prometheus metrics
http://yourdomain.com:9090
```

### Log Management

```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service logs
docker-compose -f docker-compose.prod.yml logs -f frontend
docker-compose -f docker-compose.prod.yml logs -f backend-api
```

### Health Checks

```bash
# Check service health
curl https://yourdomain.com/health
curl https://yourdomain.com/api/health
curl https://yourdomain.com/services/health
```

## Security Configuration

### Firewall Setup

```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### SSL/TLS Configuration

Nginx configuration is automatically handled by the production setup. Ensure:
- TLS 1.2+ only
- Strong cipher suites
- HSTS headers
- Certificate auto-renewal

## Backup Strategy

### Database Backups

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker exec ecommerce_postgres_prod pg_dump -U ecommerce_user ecommerce_prod | gzip > backup_${DATE}.sql.gz

# Upload to cloud storage
aws s3 cp backup_${DATE}.sql.gz s3://your-backup-bucket/database/
```

### File Backups

```bash
# Backup uploaded files
tar -czf uploads_backup_${DATE}.tar.gz uploads/
aws s3 cp uploads_backup_${DATE}.tar.gz s3://your-backup-bucket/files/
```

## Scaling

### Horizontal Scaling

```bash
# Scale specific services
docker-compose -f docker-compose.prod.yml up -d --scale backend-api=3
docker-compose -f docker-compose.prod.yml up -d --scale backend-services=2
```

### Load Balancing

Configure Nginx for load balancing:

```nginx
upstream backend_api {
    server backend-api-1:8000;
    server backend-api-2:8000;
    server backend-api-3:8000;
}

upstream backend_services {
    server backend-services-1:3001;
    server backend-services-2:3001;
}
```

## Maintenance

### Updates and Patches

```bash
# Update application
git pull origin main
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Update system packages
sudo apt update && sudo apt upgrade -y
```

### Certificate Renewal

```bash
# Renew Let's Encrypt certificates
sudo certbot renew --dry-run
sudo certbot renew

# Restart Nginx
docker-compose -f docker-compose.prod.yml restart nginx
```

## Troubleshooting

### Common Issues

1. **Service won't start**
   ```bash
   docker-compose -f docker-compose.prod.yml logs [service-name]
   ```

2. **Database connection issues**
   ```bash
   docker exec -it ecommerce_postgres_prod psql -U ecommerce_user -d ecommerce_prod
   ```

3. **SSL certificate issues**
   ```bash
   sudo certbot certificates
   openssl x509 -in /path/to/cert.pem -text -noout
   ```

### Performance Optimization

1. **Database optimization**
   - Enable connection pooling
   - Optimize queries with indexes
   - Regular VACUUM and ANALYZE

2. **Application optimization**
   - Enable Redis caching
   - Optimize Docker images
   - Use CDN for static assets

3. **Infrastructure optimization**
   - Use SSD storage
   - Adequate RAM allocation
   - Network optimization

## Rollback Procedures

### Application Rollback

```bash
# Rollback to previous version
git checkout [previous-commit]
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d
```

### Database Rollback

```bash
# Restore from backup
docker exec -i ecommerce_postgres_prod psql -U ecommerce_user ecommerce_prod < backup_previous.sql
```

## Support and Maintenance

- Monitor application logs daily
- Perform weekly security updates
- Monthly backup verification
- Quarterly security audits
- Annual disaster recovery testing
