const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const config = require('../config');
const logger = require('../utils/logger');

class UploadService {
  constructor() {
    this.storage = multer.memoryStorage();
    this.upload = multer({
      storage: this.storage,
      limits: {
        fileSize: config.upload.maxFileSize
      },
      fileFilter: this.fileFilter.bind(this)
    });
  }

  fileFilter(req, file, cb) {
    if (config.upload.allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only images are allowed.'), false);
    }
  }

  getUploadMiddleware() {
    return this.upload.single('file');
  }

  getMultipleUploadMiddleware() {
    return this.upload.array('files', 10);
  }

  async processImage(buffer, options = {}) {
    const {
      width = 800,
      height = 600,
      quality = 80,
      format = 'jpeg'
    } = options;

    try {
      const processedBuffer = await sharp(buffer)
        .resize(width, height, { 
          fit: 'inside',
          withoutEnlargement: true 
        })
        .jpeg({ quality })
        .toBuffer();

      return processedBuffer;
    } catch (error) {
      logger.error('Image processing failed', { error: error.message });
      throw error;
    }
  }

  async saveFile(buffer, filename, subfolder = '') {
    try {
      const uploadDir = path.join(config.upload.uploadDir, subfolder);
      
      // Ensure directory exists
      await fs.mkdir(uploadDir, { recursive: true });
      
      const filePath = path.join(uploadDir, filename);
      await fs.writeFile(filePath, buffer);
      
      const relativePath = path.join(subfolder, filename).replace(/\\/g, '/');
      logger.info(`File saved successfully: ${relativePath}`);
      
      return {
        filename,
        path: relativePath,
        url: `/uploads/${relativePath}`
      };
    } catch (error) {
      logger.error('File save failed', { error: error.message, filename });
      throw error;
    }
  }

  generateFilename(originalName, prefix = '') {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const ext = path.extname(originalName);
    return `${prefix}${timestamp}_${random}${ext}`;
  }

  async deleteFile(filePath) {
    try {
      const fullPath = path.join(config.upload.uploadDir, filePath);
      await fs.unlink(fullPath);
      logger.info(`File deleted successfully: ${filePath}`);
    } catch (error) {
      logger.error('File deletion failed', { error: error.message, filePath });
      throw error;
    }
  }
}

module.exports = new UploadService();
