const express = require('express');
const Joi = require('joi');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const notificationSchema = Joi.object({
  room: Joi.string().required(),
  event: Joi.string().required(),
  data: Joi.object().required()
});

// Send real-time notification
router.post('/send', (req, res) => {
  try {
    const { error, value } = notificationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const io = req.app.get('io');
    const { room, event, data } = value;

    // Send to specific room
    io.to(room).emit(event, data);

    logger.info(`Notification sent to room ${room}`, { event, data });
    res.json({ success: true, message: 'Notification sent' });
  } catch (err) {
    logger.error('Notification send failed', { error: err.message });
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

// Send notification to all connected clients
router.post('/broadcast', (req, res) => {
  try {
    const { error, value } = Joi.object({
      event: Joi.string().required(),
      data: Joi.object().required()
    }).validate(req.body);

    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const io = req.app.get('io');
    const { event, data } = value;

    // Broadcast to all clients
    io.emit(event, data);

    logger.info(`Broadcast notification sent`, { event, data });
    res.json({ success: true, message: 'Broadcast notification sent' });
  } catch (err) {
    logger.error('Broadcast notification failed', { error: err.message });
    res.status(500).json({ error: 'Failed to send broadcast notification' });
  }
});

// Get connected clients count
router.get('/clients', (req, res) => {
  try {
    const io = req.app.get('io');
    const clientsCount = io.engine.clientsCount;

    res.json({ 
      success: true, 
      clientsCount,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    logger.error('Failed to get clients count', { error: err.message });
    res.status(500).json({ error: 'Failed to get clients count' });
  }
});

module.exports = router;
